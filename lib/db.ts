import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import * as schema from './schema';

// Get DATABASE_URL from environment variables
const getDatabaseUrl = () => {
  // In Next.js, environment variables from .env.local are automatically loaded
  let databaseUrl = process.env.DATABASE_URL;

  // If not found and we're in a Node.js environment (not browser), try loading .env.local manually
  if (!databaseUrl && typeof window === 'undefined') {
    try {
      const dotenv = require('dotenv');
      const path = require('path');
      const envPath = path.resolve(process.cwd(), '.env.local');
      const result = dotenv.config({ path: envPath });
      databaseUrl = result.parsed?.DATABASE_URL || process.env.DATABASE_URL;
    } catch (e) {
      // dotenv might not be available in all environments
      console.warn('Could not load .env.local file:', e);
    }
  }

  return databaseUrl;
};

const databaseUrl = getDatabaseUrl();

if (!databaseUrl) {
  throw new Error('DATABASE_URL environment variable is required. Please check your .env.local file.');
}

const sql = neon(databaseUrl);
export const db = drizzle(sql, { schema });
