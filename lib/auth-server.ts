import { db } from './db';
import { users } from './schema';
import { eq } from 'drizzle-orm';
import type { AuthUser } from './auth';

// Simple password validation (in production, use proper hashing)
const DEMO_PASSWORD = 'Password123';

export async function authenticateUser(username: string, password: string): Promise<AuthUser | null> {
  if (password !== DEMO_PASSWORD) {
    return null;
  }

  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.username, username))
      .limit(1);

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.displayName,
      avatarUrl: user.avatarUrl || undefined,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

export async function getAllUsers(): Promise<AuthUser[]> {
  try {
    const allUsers = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        displayName: users.displayName,
        avatarUrl: users.avatarUrl,
      })
      .from(users)
      .where(eq(users.isActive, true));

    return allUsers.map(user => ({
      ...user,
      avatarUrl: user.avatarUrl || undefined,
    }));
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}
